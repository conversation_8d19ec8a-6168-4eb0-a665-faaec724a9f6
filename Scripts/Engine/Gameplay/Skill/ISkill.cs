//  Desc:        Framework For Game Develop with Unity3d
//  Copyright:   Copyright (C) 2017 SnowCold. All rights reserved.
//  WebSite:     https://github.com/SnowCold/Qarth
//  Blog:        http://blog.csdn.net/snowcoldgame
//  Author:      SnowCold
//  E-mail:      <EMAIL>
using System;
using UnityEngine;

using System.Collections;
using System.Collections.Generic;

namespace Qarth
{
    public interface ISkill
    {
        SkillInfo skillInfo { get; set; }
        AbstractSkillSystem skillSystem { get; }
        ISkillReleaser skillReleaser { get; }
        void DoSkillRelease(AbstractSkillSystem system, ISkillReleaser releaser);
        void DoSkillRemove();
        void DoSkillUpdate(float deltaTime);
    }
}
