{"name": "AloneSoft.VeryAnimation.Editor", "references": ["AloneSoft.VeryAnimation", "Unity.Timeline", "Unity.Timeline.Editor", "Unity.Animation.Rigging", "Unity.Animation.Rigging.Editor"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [{"name": "com.unity.timeline", "expression": "0.0.0-builtin", "define": "VERYANIMATION_TIMELINE"}, {"name": "com.unity.animation.rigging", "expression": "0.2.5-preview", "define": "VERYANIMATION_ANIMATIONRIGGING"}]}