Shader "Seikami/Lowpoly/GPUSkinning"
{
    Properties
    {
        [Foldout(1, 1, 0, 1)] _F_Basic ("Basic_Foldout", float) = 1
        [Tex(_MainTexColor)][NoScaleOffset] _MainTex ("Texture", 2D) = "white" { }
        [HideInInspector] _MainTexColor ("MainTexColor", color) = (1, 1, 1, 1)
        [Foldout_Out(1)] _F_Basic_Out ("Basic_Out_Foldout", float) = 1

        [Foldout(1, 1, 0, 1)] _F_Surface ("Surface_Foldout", float) = 1
        [HDR] _EmissionColor ("Emission Color", Color) = (1, 1, 1, 1)
        _EmissionIntensity ("Emission Intensity", Range(0, 5)) = 0
        _GreyIntensity ("Grey Intensity", Range(0, 1)) = 0

        [Foldout_Out(1)] _F_Surface_Out ("Surface_Out_Foldout", float) = 1

        [Foldout(1, 1, 0, 1)] _F_Dead("Dead_Foldout", float) = 1
        _DissolveMap ("Dissolve Map", 2D) = "white"{}
        _DissolveThreshold ("Dissolve Threshold", Range(0, 1)) = 0.0
        _DissolveLineWidth("Dissolve Line Width", Range(0.0, 0.5)) = 0.1
        [HDR] _DissolveColor("Dissolve First Color", Color) = (1, 0, 0, 1)
        [Foldout_Out(1)] _F_Dead_Out ("Dead_Foldout", float) = 1


        [Foldout(1, 1, 0, 1)] _F_Fresnel ("Fresnel_Foldout", float) = 1
        _Glint ("Glint", Range(0, 1)) = 0
        [Foldout_Out(1)] _F_Fresnel_Out ("Fresnel_Foldout", float) = 1


        [Foldout(1, 1, 0, 1)] _F_Advanced ("Advanced_Foldout", float) = 1
        [Toggle_Switch] _RECEIVE_SHADOWS_OFF ("Receive Shadow Off", float) = 0
        [Toggle_Switch] _GLOBALEFFECT ("Global Effect", float) = 0
        [Foldout_Out(1)] _F_Advanced_Out ("Advanced_Out_Foldout", float) = 1
    }

    SubShader
    {
        Pass
        {
            Name "ForwardLit"
            Tags
            {
                "LightMode" = "UniversalForward"
            }

            Cull Off

            HLSLPROGRAM
            // -------------------------------------
            // Universal Pipeline keywords
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #pragma multi_compile_fragment _ _SHADOWS_SOFT
            //--------------------------------------
            // GPU Instancing
            #pragma multi_compile_instancing
            // -------------------------------------
            // Unity defined keywords
            #pragma multi_compile_fragment _ DEBUG_DISPLAY
            // -------------------------------------
            // Material Keywords
            #pragma shader_feature_local _RECEIVE_SHADOWS_OFF_ON
            // #pragma shader_feature_local _GLOBALEFFECT_ON
            // #define _GLOBALEFFECT_ON

            #pragma vertex SkinVert
            #pragma fragment SkinForwardFrag

            #include "ShaderInclude/GPUSkinningInput.hlsl"
            #include "Packages/com.game.rendering.pipeline/ShaderLibrary/LowpolyLighting.hlsl"
            #include "ShaderInclude/LowpolyForwardPass.hlsl"


            Varyings SkinVert(SkinAttributes input)
            {
                Varyings output = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                float4 positionOS = skin4(input.positionOS, input.texcoord2, input.texcoord3);
                float4 normalOS = skin4(half4(input.normalOS, 0), input.texcoord2, input.texcoord3); //对发现也来一次蒙皮变化 区别就是取0
                VertexPositionInputs vertexPos = GetVertexPositionInputs(positionOS.xyz);

                // output.viewDirWS = GetWorldSpaceViewDir(vertexPos.positionWS);
                output.positionCS = vertexPos.positionCS;
                output.normalWS = TransformObjectToWorldNormal(normalOS.xyz);;
                output.positionWS = vertexPos.positionWS;
                output.uv = TRANSFORM_TEX(input.texcoord, _MainTex);

                return output;
            }

            half4 SkinForwardFrag(Varyings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input)


                //Base Color
                half4 albedoAlpha = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv);
                albedoAlpha.rgb *= _MainTexColor.rgb;
                albedoAlpha.a = 1;

                SurfaceData surfaceData = (SurfaceData)0;
                surfaceData.albedo = albedoAlpha.rgb;
                surfaceData.alpha = albedoAlpha.a;
                surfaceData.emission = _EmissionColor.rgb * _EmissionIntensity;
                surfaceData.clearCoatMask = 0;

                InputData inputData = (InputData)0;
                inputData.positionWS = input.positionWS;
                inputData.normalWS = normalize(input.normalWS);
                inputData.normalizedScreenSpaceUV = GetNormalizedScreenSpaceUV(input.positionCS);
                half4 result = LowpolyLighting(inputData, surfaceData);
                result.rgb = lerp(result.rgb, 1, _Glint);

                // #ifdef _GLOBALEFFECT_ON
                // ApplyGlobalFog(input.positionWS, result.rgb);
                // #endif


                float luminance = dot(result.rgb, half3(0.2125, 0.7154, 0.0721));
                result.rgb = lerp(result.rgb, luminance, _GreyIntensity);

                //Dissolve
                float2 dissolveUV = saturate(input.normalWS.xy * input.normalWS.yz + 0.5) * _DissolveMap_ST.xy + _DissolveMap_ST.zw;
                half4 dissolveValue = SAMPLE_TEXTURE2D(_DissolveMap, sampler_DissolveMap, dissolveUV);
                clip(dissolveValue.r - _DissolveThreshold);
                //_LineWidth控制消融颜色作用的范围。与光照颜色进行lerp
                half t = 1 - smoothstep(0.0, _DissolveLineWidth, dissolveValue.r - _DissolveThreshold);

                // return t;
                half3 finalColor = lerp(result.rgb, _DissolveColor, t * step(0.001, _DissolveThreshold));
                return half4(finalColor, 1);
                return result;
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            // Render State
            Cull Back
            Blend One Zero

            ZTest LEqual
            ZWrite On
            ColorMask 0

            // --------------------------------------------------
            // Pass

            HLSLPROGRAM
            // Pragmas
            //#pragma target 4.5
            //#pragma exclude_renderers gles gles3 glcore
            #pragma multi_compile_instancing

            #pragma vertex SkinShadowPassVertex
            #pragma fragment ShadowPassFragment

            #include "ShaderInclude/GPUSkinningInput.hlsl"
            #include "ShaderInclude/LowpolyShadowCasterPass.hlsl"

            float4 SkinGetShadowPositionHClip(SkinAttributes input)
            {
                float4 positionOS = skin4(input.positionOS, input.texcoord2, input.texcoord3);
                float3 positionWS = TransformObjectToWorld(positionOS.xyz);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, _LightDirection));

                #if UNITY_REVERSED_Z
                positionCS.z = min(positionCS.z, positionCS.w * UNITY_NEAR_CLIP_VALUE);
                #else
                positionCS.z = max(positionCS.z, positionCS.w * UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings SkinShadowPassVertex(SkinAttributes input)
            {
                Varyings output = (Varyings)0;;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                output.uv = TRANSFORM_TEX(input.texcoord, _MainTex);
                output.positionCS = SkinGetShadowPositionHClip(input);
                return output;
            }
            ENDHLSL
        }

        Pass
        {
            Name "DepthOnly"
            Tags
            {
                "LightMode" = "DepthOnly"
            }

            // -------------------------------------
            // Render State Commands
            ZWrite On
            ColorMask R
            // Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0

            // -------------------------------------
            // Shader Stages
            #pragma vertex DepthOnlyVertex
            #pragma fragment DepthOnlyFragment

            // -------------------------------------
            // Material Keywords
            // #pragma shader_feature_local _ALPHATEST_ON

            // -------------------------------------
            // Unity defined keywords
            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE

            //--------------------------------------
            // GPU Instancing
            #pragma multi_compile_instancing
            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"

            // -------------------------------------
            // Includes
            #include "ShaderInclude/GPUSkinningInput.hlsl"
            #include "ShaderInclude/GPUSkinningDepthOnlyPass.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }

            // -------------------------------------
            // Render State Commands
            ZWrite On
            // Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0

            // -------------------------------------
            // Shader Stages
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment

            // -------------------------------------
            // Unity defined keywords
            // #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE

            // -------------------------------------
            // Universal Pipeline keywords
            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/RenderingLayers.hlsl"

            //--------------------------------------
            // GPU Instancing
            #pragma multi_compile_instancing
            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"

            #include "ShaderInclude/GPUSkinningInput.hlsl"
            #include "ShaderInclude/GPUSkinningDepthNormalsPass.hlsl"
            ENDHLSL
        }


        Pass
        {
            Name "SceneSelectionPass"
            Tags
            {
                "LightMode" = "SceneSelectionPass"
            }

            HLSLPROGRAM
            #pragma target 2.0

            // -------------------------------------
            // Shader Stages
            #pragma vertex DepthVertex
            #pragma fragment SelectionFragment

            //--------------------------------------
            // GPU Instancing
            #pragma multi_compile_instancing
            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"


            int _ObjectId;
            int _PassValue;

            #include "ShaderInclude/GPUSkinningInput.hlsl"

            struct DepthVaryings
            {
                float4 positionCS : SV_POSITION;
            };

            DepthVaryings DepthVertex(SkinAttributes input)
            {
                DepthVaryings output;
                UNITY_SETUP_INSTANCE_ID(input);

                float4 positionOS = skin4(input.positionOS, input.texcoord2, input.texcoord3);
                float3 positionWS = TransformObjectToWorld(positionOS.xyz);

                output.positionCS = TransformWorldToHClip(positionWS);
                return output;
            }

            half4 SelectionFragment(DepthVaryings input) : SV_TARGET
            {
                UNITY_SETUP_INSTANCE_ID(input)
                return half4(_ObjectId, _PassValue, 1.0, 1.0);
            }
            ENDHLSL
        }
    }

    // --------------------------------------------
    CustomEditor "Scarecrow.SimpleShaderGUI"
}