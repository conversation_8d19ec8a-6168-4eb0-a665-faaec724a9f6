#ifndef WATERLAVA_INPUT_INCLUDED
#define WATERLAVA_INPUT_INCLUDED

#include "WaterCommon.hlsl"
#include "WaterPass.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareOpaqueTexture.hlsl"
// ----------------------------------------------------------
CBUFFER_START(UnityPerMaterial)
half _Tilling, _TillingSwirl;
half _Speed;
half _SwirlStrength;
half _FadeShallow;

half4 _FadeColor;
// half    _WaterSpeed;
// half    _WaterDistortScale;

// half    _NormalScale;
// half    _NormalTiling;

// half    _ReflectionStrength;

// half    _SSRMaxCount;
// half    _SSRStep;

// float3  _LightDirection;
// half    _DiffuseLightIntensity;
// half    _ShadowIntensity;

// half    _SpecularRoughness;
// half4   _SpecularColor;
// half    _SpecularIntensity;
// half    _SpecularNormalScale;
// half    _SpecularPower;
float _NIGHTCOLOR;
CBUFFER_END

// TEXTURE2D(_NormalTex);                  SAMPLER(sampler_NormalTex);
// TEXTURECUBE(_ReflectCubemap);           SAMPLER(sampler_ReflectCubemap);
// TEXTURE2D(_GrabTexture);                SAMPLER(sampler_GrabTexture);
TEXTURE2D(_MainTex);                     SAMPLER(sampler_MainTex);
TEXTURE2D(_NoiseTex);                    SAMPLER(sampler_NoiseTex);
TEXTURE2D(_SwirlTex);                    SAMPLER(sampler_SwirlTex);

// ----------------------------------------------------------

// void GetNormal(float2 uv, float3x3 tangentToWorld, float normalTiling, float normalScale,
//         inout half3 normalTS, inout half3 normalWS)
// {
//     uv *= normalTiling;

//     float2 uv_a = uv + frac(_Time.y * float2(-0.03, 0) * _WaterSpeed);
//     float2 uv_b = uv + frac(_Time.y * float2(0.04, 0.04) * _WaterSpeed);

//     half3 normalTS_a = UnpackNormalScale(SAMPLE_TEXTURE2D(_NormalTex, sampler_NormalTex, uv_a), normalScale);
//     half3 normalTS_b = UnpackNormalScale(SAMPLE_TEXTURE2D(_NormalTex, sampler_NormalTex, uv_b), normalScale);
// 	normalTS = BlendNormalRNM(normalTS_a, normalTS_b);

//     normalWS = NormalizeNormalPerPixel(TransformTangentToWorld(normalTS, tangentToWorld));
// }

// half3 GetReflectionCustom(half3 reflectVector)
// {
//     half4 reflection = SAMPLE_TEXTURECUBE_LOD(_ReflectCubemap, sampler_ReflectCubemap, reflectVector, 0);
//     reflection.rgb *= reflection.a;

//     return reflection.rgb;
// }

float2 GetScreenUV(float4 positionNDC)
{
    return positionNDC.xy * rcp(positionNDC.w);
}


void GetFadeInfo(Varyings input, float2 screenUV, half3 viewDirWS,
inout float sceneZ, inout float fade, inout float fadeShallow)
{
    // sceneZ
    float rawDepth = SAMPLE_TEXTURE2D_X(_CameraDepthTexture, sampler_CameraDepthTexture, screenUV).r;
    sceneZ = (unity_OrthoParams.w == 0) ? LinearEyeDepth(rawDepth, _ZBufferParams) : LinearDepthToEyeDepth(rawDepth);
    // fade
    float thisZ = input.positionNDC.w;// LinearEyeDepth(input.positionNDC.z / input.positionNDC.w, _ZBufferParams);
    fade = sceneZ - thisZ;
    // 浅水渐变
    float depthFix = saturate(0.3 + dot(viewDirWS, float3(0, 1, 0)));
    fadeShallow = saturate(1 - LinearToExp(fade * depthFix, _FadeShallow));
}


#endif // WATERLAVA_INPUT_INCLUDED
