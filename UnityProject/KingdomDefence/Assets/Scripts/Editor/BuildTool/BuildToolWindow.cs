using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using System;
using Qarth;

namespace GameWish.Game
{
    public class BuildToolWindow : OdinEditorWindow
    {
        [MenuItem("Tools/打包工具 (BuildTool)", false, priority: 10001)]
        static void OpenWindow()
        {
            var window = GetWindow<BuildToolWindow>();
            window.titleContent = new GUIContent("打包工具 (BuildTool)");
            window.Show();
        }

        [OnInspectorInit]
        void Init()
        {
            sdkConfig = AssetDatabase.LoadAssetAtPath<SDKConfig>("Assets/Resources/Config/SDKConfig.asset");
        }


        [InlineEditor] public SDKConfig sdkConfig;


        [BoxGroup("包体设置")]
        [LabelText("是否是debug包")]
        public bool IsDebug;

        [BoxGroup("包体设置")]
        [LabelText("显示版本号")]
        public string AppVersion = "1.0";

        [BoxGroup("包体设置")]
        [LabelText("是否构建 aab 包")]
        public bool IsBuildAppBundle = false;

        [BoxGroup("资源设置")] [LabelText("打表")] public bool IsBuildTable = true;

        [BoxGroup("资源设置")] [LabelText("打AB")] public bool IsBuildAB = true;


        [HorizontalGroup, Button(ButtonSizes.Large), GUIColor(0, 1, 0), PropertyOrder(2)]
        public void Build()
        {
            var title = "";
            title += "1.打开新手引导\n";
            title += "2.恢复系统时间\n";
            title += "3.关闭DEVELOPMENT宏\n";

            if (!EditorUtility.DisplayDialog("打包前确认", title, "确认", "取消"))
                return;

            RestoreConfig();

            SetBuildParams();
            BuildTool.Build();
        }

        [HorizontalGroup, Button(ButtonSizes.Large), GUIColor(0, 1, 0), PropertyOrder(2)]
        public void BuildAB()
        {
            SetBuildParams();
            BuildTool.BuildBundles();
        }

        // [Button(ButtonSizes.Large)]
        void RestoreConfig()
        {
            var appConfig = AssetDatabase.LoadAssetAtPath<AppConfig>("Assets/Resources/Config/AppConfig.asset");
            if (appConfig != null)
            {
                SerializedObject so = new SerializedObject(appConfig);
                so.FindProperty("m_IsGuideActive").boolValue = true;
                so.ApplyModifiedProperties();
                EditorUtility.SetDirty(appConfig);
                AssetDatabase.SaveAssetIfDirty(appConfig);
            }
        }


        private void SetBuildParams()
        {
            Environment.SetEnvironmentVariable(BuildArg.IsDebug.ToString(), IsDebug.ToString());
            Environment.SetEnvironmentVariable(BuildArg.AppVersion.ToString(), AppVersion);
            Environment.SetEnvironmentVariable(BuildArg.IsBuildAppBundle.ToString(), IsBuildAppBundle.ToString());
            Environment.SetEnvironmentVariable(BuildArg.IsBuildTable.ToString(), IsBuildTable.ToString());
            Environment.SetEnvironmentVariable(BuildArg.IsBuildAB.ToString(), IsBuildAB.ToString());
        }
    }
}