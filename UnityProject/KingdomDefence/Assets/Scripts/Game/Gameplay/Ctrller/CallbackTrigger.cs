using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

namespace GameWish.Game
{
    [RequireComponent(typeof(Collider))]
    public class CallbackTrigger : MonoBehaviour
    {
        [SerializeField] protected Collider m_Trigger;
        [SerializeField] protected Collider m_Collider;
        public Action<Collider> onEnter;
        public Action<Collider> onExit;

        protected virtual void OnTriggerEnter(Collider other)
        {
            onEnter?.Invoke(other);
        }

        protected virtual void OnTriggerExit(Collider other)
        {
            onExit?.Invoke(other);
        }

        public void CleanEvt()
        {
            onEnter = null;
            onExit = null;
        }

        public void SetTriggerState(bool enable)
        {
            if (m_Trigger != null)
                m_Trigger.enabled = enable;
        }

        public bool IsTriggerEnabled()
        {
            if (m_Trigger != null)
                return m_Trigger.enabled;
            return false;
        }

        public void SetColliderState(bool enable)
        {
            if (m_Collider != null)
                m_Collider.enabled = enable;
        }

        public void SetTriggerCenter(Vector3 center)
        {
            if (m_Trigger is SphereCollider)
                (m_Trigger as SphereCollider).center = center;
            else if (m_Trigger is BoxCollider)
                (m_Trigger as BoxCollider).center = center;
        }

        public void SetTriggerSize(float size)
        {
            if (m_Trigger != null)
                (m_Trigger as SphereCollider).radius = size;
        }

        public void SetTriggerSize(Vector3 size)
        {
            if (m_Trigger != null)
                (m_Trigger as BoxCollider).size = size;
        }

        public float GetSphereTriggerSize()
        {
            if (m_Trigger != null && m_Trigger is SphereCollider)
            {
                return (m_Trigger as SphereCollider).radius;
            }

            return 0;
        }
    }
}