using System;
using UnityEngine;
using Qarth;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace GameWish.Game
{
    public class SpinAtkComponent : EntityComponent
    {
        private Transform m_TrsBody;
        private Transform m_TransSpinRoot;
        private EntityBattleRTData m_RTData;
        private SpinWeaponCtrller[] m_LstCurSpinWeaponCtrl;
        private int m_WeaponCount = 1;
        private float m_WeaponRadius = 1;
        private Vector3 m_WeaponRotateSpd = new Vector3(0, 240, 0);

        private SpinWeaponCtrller[] m_LstExtraSpinWeaponCtrl;

        private int m_CurrentWeaponNum = 0;

        public int weaponCountByAd = 0;
        public int weaponCountByChapterBuff = 0;
        public int weaponCountDoubleRate = 1;
        public float rotateSpdAdd = 0;
        public float hitBackPow = 0;
        public float sizeAdd = 0;

        public int CurrentWeaponNum
        {
            get { return m_CurrentWeaponNum; }
        }

        public SpinAtkComponent(Transform transRoot, EntityBattleRTData rtData)
        {
            m_CurrentWeaponNum = 0;
            m_TrsBody = transRoot;
            m_RTData = rtData;
            if (m_TransSpinRoot == null)
            {
                m_TransSpinRoot = new GameObject("spinRoot").transform;
                m_TransSpinRoot.gameObject.SetAllLayer(LayerMask.NameToLayer("SpinWeapon"));
                var rig = m_TransSpinRoot.gameObject.AddMissingComponent<Rigidbody>();
                rig.isKinematic = true;
            }
        }
        public override void InitComponent(EntityMonoBase owner)
        {
            base.InitComponent(owner);
            EventSystem.S.Register(EventID.OnAddKnifeNumByAd, OnAddKnifeNumByAd);
            EventSystem.S.Register(EventID.OnRefreshChapterBuff, OnRefreshChapterBuff);
            EventSystem.S.Register(EventID.OnRefreshRuneStoneBuff, OnRefreshChapterBuff);
            m_Owner.RegisterSelfEvt(EventID.OnAddExtraWeapon, OnAddExtraWeapon);
            m_Owner.RegisterSelfEvt(EventID.OnAddConstWeapon, OnAddConstWeapon);
            LoadWeapon();
        }
        void OnRefreshChapterBuff(int key, params object[] para)
        {
            hitBackPow = 0;
            rotateSpdAdd = 0;
            weaponCountByChapterBuff = 0;
            weaponCountDoubleRate = 1;
            sizeAdd = 0;
            ChapterBuffMgr.S.HandleReceiverParams(m_RTData.battleOwner, this);
            RunestoneMgr.S.HandleReceiverParams(m_RTData.battleOwner, this);
            LoadWeapon();
        }
        void OnAddKnifeNumByAd(int key, params object[] para)
        {
            weaponCountByAd++;
            LoadWeapon();
        }
        void OnAddConstWeapon(int key, params object[] para)
        {
            m_WeaponCount += (int)para[0];
            LoadWeapon();
        }
        void OnAddExtraWeapon(int key, params object[] para)
        {
            bool isOpen = (bool)para[0];
            if (isOpen)
            {
                if (m_LstExtraSpinWeaponCtrl == null)
                {
                    int num = (int)para[1];
                    float dmgRatio = (float)para[2];
                    LoadExtraWeapon(num, dmgRatio);
                }
                else
                {
                    if (m_LstExtraSpinWeaponCtrl != null)
                    {
                        foreach (var item in m_LstExtraSpinWeaponCtrl)
                        {
                            item.gameObject.SetObjActive(true);
                        }
                    }
                }
            }
            else
            {
                if (m_LstExtraSpinWeaponCtrl != null)
                {
                    foreach (var item in m_LstExtraSpinWeaponCtrl)
                    {
                        item.gameObject.SetObjActive(false);
                    }
                }
            }
        }
        void LoadExtraWeapon(int num, float dmgRatio)
        {
            AddressableResMgr.S.InstantiateAsync(WorldInfoMgr.data.upgradeData.GetUpgradeItem(Define.PLAYER_CLOSEWEAPON_UPID).GetCurPrefab(),
                           (obj, state) =>
                           {
                               if (state)
                               {
                                   m_LstExtraSpinWeaponCtrl = new SpinWeaponCtrller[num];
                                   obj.AddMissingComponent<AddressableGameObjectSelfCleaner>();
                                   obj.transform.SetParent(m_TransSpinRoot);
                                   obj.transform.localScale = Vector3.one;

                                   m_LstExtraSpinWeaponCtrl[0] = obj.GetComponent<SpinWeaponCtrller>();

                                   if (num > 1)
                                   {
                                       for (int i = 1; i < num; i++)
                                       {
                                           var item = GameObject.Instantiate(obj);
                                           item.transform.SetParent(m_TransSpinRoot);
                                           item.transform.localScale = Vector3.one;
                                           m_LstExtraSpinWeaponCtrl[i] = item.GetComponent<SpinWeaponCtrller>();
                                       }
                                   }

                                   for (int i = 0; i < m_LstExtraSpinWeaponCtrl.Length; i++)
                                   {
                                       // 计算每个物体在圆上的角度位置（弧度制）
                                       float angle = i * (2f * Mathf.PI / m_LstExtraSpinWeaponCtrl.Length) + 90;

                                       // 计算物体在圆上的位置
                                       float x = Mathf.Cos(angle) * m_WeaponRadius * 3;
                                       float z = Mathf.Sin(angle) * m_WeaponRadius * 3;
                                       Vector3 localPos = new Vector3(x, 0f, z);
                                       // 计算朝向中心点的旋转角度（使用atan2计算目标角度）
                                       float targetAngle = Mathf.Atan2(x, z) * Mathf.Rad2Deg;
                                       // 应用旋转（仅绕Y轴）
                                       m_LstExtraSpinWeaponCtrl[i].transform.localPosition = localPos;
                                       m_LstExtraSpinWeaponCtrl[i].transform.localRotation = Quaternion.Euler(0f, targetAngle, 0f);

                                       m_LstExtraSpinWeaponCtrl[i].atkEnemy = HandlerEnemyDmg;
                                       m_LstExtraSpinWeaponCtrl[i].atkTree = HandlerWoodDmg;
                                       m_LstExtraSpinWeaponCtrl[i].dmgRatio = dmgRatio;
                                   }
                               }
                           });
        }
        void LoadWeapon()
        {
            m_TransSpinRoot.localScale = Vector3.one * (1 + sizeAdd);
            if (m_CurrentWeaponNum == (weaponCountByChapterBuff + m_WeaponCount + weaponCountByAd) * weaponCountDoubleRate)
            {
                return;
            }
            m_CurrentWeaponNum = (weaponCountByChapterBuff + m_WeaponCount + weaponCountByAd) * weaponCountDoubleRate;
            EventSystem.S.Send(EventID.OnUpdateWeaponNum);
            AddressableResMgr.S.InstantiateAsync(WorldInfoMgr.data.upgradeData.GetUpgradeItem(Define.PLAYER_CLOSEWEAPON_UPID).GetCurPrefab(),
                (obj, state) =>
                {
                    if (state)
                    {
                        if (m_LstCurSpinWeaponCtrl != null)
                        {
                            for (int i = 0; i < m_LstCurSpinWeaponCtrl.Length; i++)
                            {
                                GameObject.Destroy(m_LstCurSpinWeaponCtrl[i].gameObject);
                            }
                        }

                        m_LstCurSpinWeaponCtrl = new SpinWeaponCtrller[m_CurrentWeaponNum];
                        obj.AddMissingComponent<AddressableGameObjectSelfCleaner>();
                        obj.transform.SetParent(m_TransSpinRoot);
                        obj.transform.localScale = Vector3.one;

                        m_LstCurSpinWeaponCtrl[0] = obj.GetComponent<SpinWeaponCtrller>();

                        if (m_CurrentWeaponNum > 1)
                        {
                            for (int i = 1; i < m_CurrentWeaponNum; i++)
                            {
                                var item = GameObject.Instantiate(obj);
                                item.transform.SetParent(m_TransSpinRoot);
                                item.transform.localScale = Vector3.one;
                                m_LstCurSpinWeaponCtrl[i] = item.GetComponent<SpinWeaponCtrller>();
                            }
                        }

                        for (int i = 0; i < m_LstCurSpinWeaponCtrl.Length; i++)
                        {
                            // 计算每个物体在圆上的角度位置（弧度制）
                            float angle = i * (2f * Mathf.PI / m_CurrentWeaponNum);

                            // 计算物体在圆上的位置
                            float x = Mathf.Cos(angle) * m_WeaponRadius;
                            float z = Mathf.Sin(angle) * m_WeaponRadius;
                            Vector3 localPos = new Vector3(x, 0f, z);
                            // 计算朝向中心点的旋转角度（使用atan2计算目标角度）
                            float targetAngle = Mathf.Atan2(x, z) * Mathf.Rad2Deg;
                            // 应用旋转（仅绕Y轴）
                            m_LstCurSpinWeaponCtrl[i].transform.localPosition = localPos;
                            m_LstCurSpinWeaponCtrl[i].transform.localRotation = Quaternion.Euler(0f, targetAngle, 0f);

                            m_LstCurSpinWeaponCtrl[i].atkEnemy = HandlerEnemyDmg;
                            m_LstCurSpinWeaponCtrl[i].atkTree = HandlerWoodDmg;
                            m_LstCurSpinWeaponCtrl[i].dmgRatio = 1;
                        }
                    }
                });
        }
        public override void DoClean()
        {
            base.DoClean();
            EventSystem.S.UnRegister(EventID.OnAddKnifeNumByAd, OnAddKnifeNumByAd);
            EventSystem.S.UnRegister(EventID.OnRefreshChapterBuff, OnRefreshChapterBuff);
            EventSystem.S.UnRegister(EventID.OnRefreshRuneStoneBuff, OnRefreshChapterBuff);
            if (m_LstCurSpinWeaponCtrl != null)
            {
                for (int i = 0; i < m_LstCurSpinWeaponCtrl.Length; i++)
                {
                    GameObject.Destroy(m_LstCurSpinWeaponCtrl[i].gameObject);
                }
                m_LstCurSpinWeaponCtrl = null;
            }
            if (m_LstExtraSpinWeaponCtrl != null)
            {
                for (int i = 0; i < m_LstExtraSpinWeaponCtrl.Length; i++)
                {
                    GameObject.Destroy(m_LstExtraSpinWeaponCtrl[i].gameObject);
                }
                m_LstExtraSpinWeaponCtrl = null;
            }
            if (m_TransSpinRoot != null)
            {
                GameObject.Destroy(m_TransSpinRoot.gameObject);
                m_TransSpinRoot = null;
            }
        }
        public override void DoTick(float deltaTime)
        {
            if (m_TransSpinRoot != null)
            {
                m_TransSpinRoot.transform.position = m_Owner.transform.position + Vector3.up * 0.5f;
                m_TransSpinRoot.Rotate(m_WeaponRotateSpd * deltaTime * (1 + rotateSpdAdd));
            }
        }
        public void HandlerWoodDmg(CutTreeCtrller ctrl, float dmgRatio = 1)
        {
            EventSystem.S.Send(EventID.OnHitDmg, ctrl.entityID, m_RTData.CreateAtkCutWoodDmgPack(dmgRatio: dmgRatio));
        }
        public void HandlerEnemyDmg(EnemyCtrller ctrl, float dmgRatio = 1)
        {
            var dmgPac = m_RTData.CreateAtkDmgPack(dmgRatio: dmgRatio);
            dmgPac.hitbackPow += hitBackPow;
            EventSystem.S.Send(EventID.OnHitDmg, ctrl.entityID, dmgPac);
            m_Owner.SendSelfEvt(EventID.OnDoCloseWeaponHit);
        }
    }
}
