using System;
using UnityEngine;
using Qarth;
using System.Collections;
using PrimeTween;
using Unity.Collections;

namespace GameWish.Game
{
    public class ParabolaMoveBullet : RaycastBulletCtrller
    {
        private int m_JumpCounter;
        [SerializeField] private GameObject m_ObjEff;
        [SerializeField] private bool m_OnlyGroundHit;
        [SerializeField] private bool m_TickObjAngle;
        private bool m_Hitted;
        private Vector3 m_PreviousPosition;

        #region parabola_params

        [SerializeField] private float m_HeightBaseDis = 10f;
        [SerializeField] private float m_HeightMax = 3f;
        [SerializeField] private float m_HeightMin = 0.2f;

        [SerializeField] private bool m_HitOnMoving = true;

        private Vector3 m_Start;
        private Vector3 m_ControlPt;

        private float m_Length;
        private float m_Progress;
        private bool m_PathInitialized;

        #endregion

        public override void ActiveBullet(TDBulletConf conf, IBattleEntity caster, DmgPack dmgPack, Transform desTrs,
            Vector3 desPos,
            int targetLayer, Action hitCB, Action<EntityMonoBase> hitEntityCB = null, BulletModifier modifier = null)
        {
            base.ActiveBullet(conf, caster, dmgPack, desTrs, desPos, targetLayer, hitCB, hitEntityCB, modifier);
            InitPath();
        }


        public override void Register()
        {
            base.Register();
            // m_AutoKill = false;
            m_ObjEff.SetActive(true);
            m_Hitted = false;
            m_Progress = 0;
            m_JumpCounter = m_ConfHolder.isChain;

            m_PreviousPosition = transform.position;
        }

        protected override void TickOnActive(float deltaTime)
        {
            base.TickOnActive(deltaTime);
            //抛物线运动
            if (m_PathInitialized && !m_Hitted)
            {
                transform.position = GetPosition(deltaTime);
                if (m_TickObjAngle)
                    GetAngle();
                m_PreviousPosition = transform.position;

                if (!m_Hitted && m_HitOnMoving)
                    TickRaycasting(deltaTime);

                if (m_Progress >= 1f && !m_Hitted)
                {
                    m_Hitted = true;
                    // Log.e(1);
                    if (m_ConfHolder != null)
                    {
                        if (!m_HitOnMoving)
                            LoadHitCastBullet();

                        if (m_ConfHolder.hitBoomRadius <= 0)
                        {
                            EffectControl.S.PlayPosEffect(m_Conf.hitEffect, m_DesPos);
                            DestroySelf();
                        }
                        else
                        {
                            m_ObjEff.SetActive(false);
                            m_Exploded = true;
                            SetMovableState(false);
                            if (HandleExplode())
                                CheckJump();
                        }
                    }
                }
            }
        }

        private void OnDisable()
        {
            transform.DOKill();
            m_PathInitialized = false;
            StopAllCoroutines();
        }

        public override void DestroySelf()
        {
            base.DestroySelf();
            m_PathInitialized = false;
        }

        protected override void HandleHit(EntityMonoBase hitEntity)
        {
            base.HandleHit(hitEntity);
            if (m_Exploded)
                return;

            CheckJump();
        }

        void CheckJump()
        {
            //抛物线型子弹不能与穿透并存，只判断是否能弹射，否则直接销毁
            if (m_JumpCounter <= 0)
            {
                m_CanDestroy = true;
            }
            else
            {
                m_CanDestroy = false;
                var targets =
                    BattleGroundMgr.S.GetOpponentInDis(m_TargetLayer, transform.position, m_ConfHolder.speed / 2f);

                if (targets.Count > 0)
                {
                    m_DesPos = targets[RandomHelper.Range(0, targets.Count)].GetEntityPosition();
                    m_DesPos.y = transform.position.y;
                    transform.LookAtXZ(m_DesPos);
                    SetMoveDesire((m_DesPos - transform.position));
                    InitPath();
                    m_ObjEff.SetActive(true);
                    m_Hitted = false;
                    m_Exploded = false;
                    m_JumpCounter -= 1;
                }
                else
                {
                    m_CanDestroy = true;
                }
            }
        }

        protected override bool HandleExplode()
        {
            var isHit = false;
            var maxhits = 100;
            var commands = new NativeArray<OverlapSphereCommand>(1, Allocator.TempJob);
            var results = new NativeArray<ColliderHit>(maxhits, Allocator.TempJob);
            commands[0] = new OverlapSphereCommand(transform.position + transform.rotation * m_CastCenterOffset, m_ConfHolder.hitBoomRadius, m_HitQueryParams);
            OverlapSphereCommand.ScheduleBatch(commands, results, 1, maxhits).Complete();

            foreach (var hit in results)
            {
                if (hit.collider != null)
                {
                    OnExplodeHit(hit.collider);
                    isHit = true;
                }
            }

            commands.Dispose();
            results.Dispose();

            if (m_OnlyGroundHit)
            {
                var pos = transform.position;
                pos.y = 0.02f;
                EffectControl.S.PlayPosEffect(m_Conf.hitEffect, pos);
            }
            else
                EffectControl.S.PlayPosEffect(m_Conf.hitEffect, transform.position);

            m_CanDestroy = true;
            return isHit;
        }


        #region parabola path

        /// <summary> 初始化抛物线运动轨迹 </summary>
        /// <returns></returns>
        private void InitPath()
        {
            // 求出最高点
            m_Start = transform.position;
            if (m_ConfHolder.speed > Mathf.Epsilon)
            {
                var disVec = m_DesPos - m_Start;
                disVec.y = 0f;
                var offset = disVec.normalized * m_ConfHolder.speed / 40f;
                m_DesPos += offset;
            }

            m_Length = Vector3.Distance(m_Start, m_DesPos);

            // 根据距离调整抛物线高度
            float adjustedHeight = Mathf.Lerp(m_HeightMin, m_HeightMax, m_Length / m_HeightBaseDis); // 假设10为远距离基准
            adjustedHeight = Mathf.Clamp(adjustedHeight, m_HeightMin, m_HeightMax);

            m_ControlPt = (m_Start + m_DesPos) / 2 + Vector3.up * adjustedHeight;

            m_Progress = 0f;
            m_PathInitialized = true;
        }


        /// <summary>
        /// 获取某个时间点的位置
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        private Vector3 GetPosition(float time)
        {
            if (time == 0 || m_Length < Mathf.Epsilon)
            {
                return m_Start;
            }

            m_Progress += m_ConfHolder.speed * time / m_Length;

            if (m_Progress >= 1f)
            {
                m_Progress = 1f;
                return m_DesPos;
            }

            return GetParabolicPosition(m_Start, m_ControlPt, m_DesPos, m_Progress);
        }

        /// <summary>
        /// 计算抛物线上的点
        /// </summary>
        Vector3 GetParabolicPosition(Vector3 start, Vector3 control, Vector3 end, float t)
        {
            // 二次贝塞尔曲线公式
            return Mathf.Pow(1 - t, 2) * start +
                   2 * (1 - t) * t * control +
                   Mathf.Pow(t, 2) * end;
        }

        public void GetAngle()
        {
            if (m_ObjEff == null)
                return;

            if (m_Progress > 0 && m_Progress < 1f)
            {
                Vector3 dir = transform.position - m_PreviousPosition;
                m_ObjEff.transform.rotation = Quaternion.LookRotation(dir);
            }
        }

        #endregion
    }
}