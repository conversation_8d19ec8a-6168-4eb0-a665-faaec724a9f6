using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public class ChapterBuffFunction_CampFireHpRecoverAddRatio : AbstractChapterBuffFunction
    {
        private float m_HpRecoverAddRatio;
        public override void Setup(ChapterBuffFunctionConfig conf)
        {
            if (conf is ChapterBuffFunctionConfig_CampFireHpRecoverAddRatio _Config)
            {
                m_HpRecoverAddRatio = _Config.hpRecoverAddRatio;
            }
        }
        public override void Init()
        {
            base.Init();
        }
        public override void DoTick(float dt)
        {
            base.DoTick(dt);
        }
        public override bool IsSuitable(IBattleEntity entity, params object[] args)
        {
            if (entity is CampFireCtrller && args.Length > 0 && args[0] is BuildingFunction_RangeHpRecover)
            {
                return true;
            }
            return false;
        }

        public override void HandleAttribute(IBattleEntity entity, ref EntityBattleBaseAttribute attr)
        {
        }

        public override void HandleReceiver(IBattleEntity entity, params object[] args)
        {
            var func = (BuildingFunction_RangeHpRecover)args[0];
            func.hpRecoverAddRatio += m_HpRecoverAddRatio;
        }
        public override void Clean()
        {
            ObjectPool<ChapterBuffFunction_CampFireHpRecoverAddRatio>.S.Recycle(this);
        }
    }
}
