using UnityEngine;
using Qarth;
using Animancer;

namespace GameWish.Game
{
    public class RoleAIState_Dead : RoleAIState
    {
        public RoleAIState_Dead(RoleAIComponent owner, RoleAIComponent.AIStateEnum stateEnum) :
            base(owner, stateEnum)
        {
        }

        public override void Enter(RoleBaseCtrller mgr)
        {
            base.Enter(mgr);
            m_AIComponent.SetGlobalState(RoleAIComponent.AIStateEnum.Dead);
        }

        public override void Execute(RoleBaseCtrller mgr, float dt)
        {
            base.Execute(mgr, dt);
        }

        public override void Exit(RoleBaseCtrller mgr)
        {
            base.Exit(mgr);
        }

        public override void DisposeState()
        {
        }
    }
}