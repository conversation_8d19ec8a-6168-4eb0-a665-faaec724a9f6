using UnityEngine;
using Qarth;
using Animancer;

namespace GameWish.Game
{
    public class EnemyFlyAIState_Thinking : EnemyAIState_Thinking
    {
        public EnemyFlyAIState_Thinking(RoleAIComponent owner,
            RoleAIComponent.AIStateEnum stateEnum, bool ignoreHeroOnInit, bool ignoreSoldierOnInit,
            float pathDis = 0.3f) : base(owner, stateEnum, ignoreHeroOnInit, ignoreSoldierOnInit, pathDis)
        {
        }
        
        public override void Enter(RoleBaseCtrller mgr)
        {
            base.Enter(mgr);
            m_CheckWall = false;
        }
    }
}