using UnityEngine;
using Qarth;
using PrimeTween;

namespace GameWish.Game
{
    public class SiegeEnemyCtrller : EnemyCtrller
    {
        protected override void SetupAI()
        {
            base.SetupAI();
            m_AIComponent.RegisterState(RoleAIComponent.AIStateEnum.Thinking,
                new EnemySiegeAIState_Thinking(m_AIComponent, RoleAIComponent.AIStateEnum.Thinking,
                    true, true,
                    m_PathingStopDis));
        }

        protected override void HandleDeadBody()
        {
            m_AnimComponent.PlayDissolveEffect(1.0f, null);
            transform.DOMoveY(-5, 1f).SetDelay(1f).OnComplete(() => { DestroySelf(); });
        }
    }
}