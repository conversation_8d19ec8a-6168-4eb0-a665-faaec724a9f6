using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public class SkillFuncModifierHandler_Duration : AbstractSkillFuncModifierHandler
    {
        public override void ModifyBeforeTrigger(SkillFuncParamHolder conf)
        {
            conf.areaDuration += GetFloatModifierVal();
        }

        protected override void DoOnRecycle()
        {
            ObjectPool<SkillFuncModifierHandler_Duration>.S.Recycle(this);
        }
    }
}