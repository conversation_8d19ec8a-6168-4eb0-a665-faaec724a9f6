using System.Collections.Generic;
using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public abstract class AbstractBuffHandler : DisposableObject, ICacheAble
    {
        private bool m_CacheFlag = false;

        protected IBattleEntity m_Caster;
        protected IBattleEntity m_Receiver;
        protected EntityBattleRTData m_RTData;
        protected TDBuffConf m_Conf;

        public float dataVal;
        public float duration;
        public float tickInterval;
        public int stackCount;

        protected float m_TickCounter;

        public int id
        {
            get { return m_Conf != null ? m_Conf.id : -1; }
        }

        public List<int> oppositeIds
        {
            get { return m_Conf != null ? m_Conf.lstOppositeBuffs : null; }
        }

        public bool cacheFlag
        {
            get { return m_CacheFlag; }

            set { m_CacheFlag = value; }
        }

        public void OnCacheReset()
        {
            // Log.e("recycled");
            m_Caster = null;
            m_RTData = null;
            m_Conf = null;
            m_Receiver = null;
        }

        public static AbstractBuffHandler Allocate(int id, float duration, float interval = -1f,
            IBattleEntity caster = null, DmgValGroup srcDmgValGroup = default, int initStack = 0)
        {
            var conf = TDBuffConfTable.GetData(id);
            if (conf == null)
            {
                Log.e("BuffConf is null, id = " + id);
                return null;
            }

            AbstractBuffHandler handler = null;
            switch (conf.typeEnum)
            {
                case EntityBattleBuffType.MaxHpRatioUp:
                // 攻击力
                case EntityBattleBuffType.AtkUp:
                case EntityBattleBuffType.AtkRatioUp:
                // 防御力
                case EntityBattleBuffType.DefUp:
                case EntityBattleBuffType.DefRatioUp:
                // 移速
                case EntityBattleBuffType.MoveSpdUp:
                case EntityBattleBuffType.MoveSpdRatioUp:
                // 攻速
                case EntityBattleBuffType.AtkSpdUp:
                case EntityBattleBuffType.AtkSpdRatioUp:
                case EntityBattleBuffType.AtkCDUp:
                // 定身
                case EntityBattleBuffType.Stun:
                // 定身
                case EntityBattleBuffType.Palsy:
                // 无敌
                case EntityBattleBuffType.IsGod:
                //吸血
                case EntityBattleBuffType.LeechRatioUp:
                //技能伤害
                case EntityBattleBuffType.SkillDmgRatioUp:
                //暴击率
                case EntityBattleBuffType.CritRatioUp:
                case EntityBattleBuffType.CritDmgRatioUp:
                //闪避率
                case EntityBattleBuffType.DodgeRatioUp:
                case EntityBattleBuffType.EleVulnerableRatioUp:
                case EntityBattleBuffType.DmgReduceRatioUp:
                case EntityBattleBuffType.ReboundDmgRatioUp:
                case EntityBattleBuffType.MoreDrop:
                //抗性
                case EntityBattleBuffType.NormalResistRatioUp:
                case EntityBattleBuffType.PierceResistRatioUp:
                case EntityBattleBuffType.LightningResistRatioUp:
                case EntityBattleBuffType.SiegeResistRatioUp:
                case EntityBattleBuffType.ExplosionResistRatioUp:
                case EntityBattleBuffType.FireResistRatioUp:
                case EntityBattleBuffType.IceResistRatioUp:
                case EntityBattleBuffType.PoisonResistRatioUp:
                case EntityBattleBuffType.ArcaneResistRatioUp:
                case EntityBattleBuffType.SealAttack:
                case EntityBattleBuffType.SealSkill:
                case EntityBattleBuffType.ClearSeal:
                    handler = ObjectPool<BuffHandler_BaseAttribute>.S.Allocate();
                    break;
                // hot
                case EntityBattleBuffType.RecoverHp:
                case EntityBattleBuffType.RecoverMaxHpRatio:
                // dot
                case EntityBattleBuffType.Dmg:
                case EntityBattleBuffType.DmgRatioHp:
                case EntityBattleBuffType.DmgRatioMaxHp:
                case EntityBattleBuffType.FireDmg:
                case EntityBattleBuffType.FireDmgRatioHp:
                case EntityBattleBuffType.FireDmgRatioMaxHp:
                case EntityBattleBuffType.FireDmgRatioSrcDmg:
                case EntityBattleBuffType.PoisonDmg:
                case EntityBattleBuffType.PoisonDmgRatioHp:
                case EntityBattleBuffType.PoisonDmgRatioMaxHp:
                    handler = ObjectPool<BuffHandler_TickData>.S.Allocate();
                    break;
                case EntityBattleBuffType.ArcaneAccumulate:
                    handler = ObjectPool<BuffHandler_ArcaneAccumulate>.S.Allocate();
                    break;
                case EntityBattleBuffType.IceSpdDown:
                    handler = ObjectPool<BuffHandler_IceSpdDown>.S.Allocate();
                    break;
                case EntityBattleBuffType.Frozen:
                    handler = ObjectPool<BuffHandler_Frozen>.S.Allocate();
                    break;
                case EntityBattleBuffType.Taunt:
                    handler = ObjectPool<BuffHandler_Taunt>.S.Allocate();
                    break;
                case EntityBattleBuffType.Virus:
                    handler = ObjectPool<BuffHandler_Virus>.S.Allocate();
                    break;
                case EntityBattleBuffType.StrongRevive:
                    handler = ObjectPool<BuffHandler_StrongRevive>.S.Allocate();
                    break;
                default:
                    Debug.LogError($"No Valid Buff:{conf.id}--{conf.typeName}");
                    break;
            }

            if (handler != null)
            {
                handler.SetupBuffData(conf, duration, interval, caster, srcDmgValGroup, initStack);
            }

            return handler;
        }

        public virtual void SetupBuffData(TDBuffConf conf, float dur, float interval = -1f, IBattleEntity caster = null,
            DmgValGroup srcDmgValGroup = default, int initStack = 0)
        {
            m_Conf = conf;
            this.duration = dur;
            m_TickCounter = this.tickInterval = interval;
            this.stackCount = m_Conf.stackCount;
            this.dataVal = m_Conf.dataVal;
            this.m_Caster = caster;
            if (initStack > 0)
                this.stackCount += initStack;
        }

        public AbstractBuffHandler SetOverrideDataVal(float newDataVal)
        {
            if (newDataVal != 0)
                this.dataVal = newDataVal;
            return this;
        }

        public TDBuffConf GetConf()
        {
            return m_Conf;
        }

        public virtual void SetupBuffReceiver(IBattleEntity receiver, EntityBattleRTData rtData)
        {
            m_Receiver = receiver;
            m_RTData = rtData;
        }

        public abstract void HandleBuffAdd();

        public virtual void HandleBuffTick(float deltaTime)
        {
            if (tickInterval <= 0)
                return;

            if (m_TickCounter >= 0)
            {
                m_TickCounter -= deltaTime;
                if (m_TickCounter < 0)
                {
                    m_TickCounter += tickInterval;
                    OnBuffTick();
                }
            }
        }

        public virtual void OnBuffTick()
        {
        }

        public abstract void HandleBuffRemove();

        public virtual void HandleBuffStack(AbstractBuffHandler other)
        {
            if (other.duration > this.duration)
                this.duration = other.duration;
            this.tickInterval = other.tickInterval;
        }

        public bool IsStrongCtrlBuff()
        {
            if (m_Conf == null)
                return false;

            switch (m_Conf.typeEnum)
            {
                case EntityBattleBuffType.Frozen:
                case EntityBattleBuffType.Stun:
                case EntityBattleBuffType.Taunt:
                    return true;
            }

            return false;
        }

        public bool IsDebuff()
        {
            if (m_Conf == null)
                return false;

            switch (m_Conf.typeEnum)
            {
                case EntityBattleBuffType.Frozen:
                case EntityBattleBuffType.Stun:
                case EntityBattleBuffType.Taunt:
                case EntityBattleBuffType.IceSpdDown:
                case EntityBattleBuffType.PoisonDmg:
                case EntityBattleBuffType.PoisonDmgRatioHp:
                case EntityBattleBuffType.PoisonDmgRatioMaxHp:
                case EntityBattleBuffType.FireDmg:
                case EntityBattleBuffType.FireDmgRatioSrcDmg:
                case EntityBattleBuffType.FireDmgRatioHp:
                case EntityBattleBuffType.FireDmgRatioMaxHp:
                case EntityBattleBuffType.Virus:
                    return true;
                default:
                    return dataVal < 0;
            }

            return false;
        }
    }
}