using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public class AudioSoundMgr : TMonoSingleton<AudioSoundMgr>
    {
        public string waveSuccess;
        public string chopTree;
        public string rewardBoxOpen;
        public string skillPanelOpen;
        public string skillSelected;
        public string adWeaponOpen;
        public string enemyHitByWeapon;
    }
}
