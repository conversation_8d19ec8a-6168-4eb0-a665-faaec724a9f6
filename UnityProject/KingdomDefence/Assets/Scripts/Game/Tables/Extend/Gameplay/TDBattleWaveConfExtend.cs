using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;
using System.Linq;
using UnityEditor;

namespace GameWish.Game
{
    public partial class TDBattleWaveConf
    {
        private int[] m_LstKillBoxReward;
        private int[] m_LstCreatorIds;
        private int[] m_LstCreatorNums;
        private int[] m_LstAloneCreatorId;
        private int[] m_LstAloneCreatorNum;

        public int[] LstKillBoxReward => m_LstKillBoxReward;
        public int[] LstCreatorIds => m_LstCreatorIds;
        public int[] LstCreatorNums => m_LstCreatorNums;
        public int[] LstAloneCreatorId => m_LstAloneCreatorId;
        public int[] LstAloneCreatorNum => m_LstAloneCreatorNum;

        public int GetBattleWaveKillBoxReward(int index)
        {
            if (index < 0)
            {
                return m_LstKillBoxReward[0];
            }
            if (index < m_LstKillBoxReward.Length)
            {
                return m_LstKillBoxReward[index];
            }
            return m_LstKillBoxReward[m_LstKillBoxReward.Length - 1];
        }

        public void Reset()
        {
            m_LstKillBoxReward = Helper.String2IntArray(killBoxReward, ";");
            m_LstCreatorIds = Helper.String2IntArray(creatorId, "|");
            m_LstCreatorNums = Helper.String2IntArray(creatorNum, "|");
            m_LstAloneCreatorId = Helper.String2IntArray(aloneCreatorId, "|");
            m_LstAloneCreatorNum = Helper.String2IntArray(aloneCreatorNum, "|");
        }
    }
}