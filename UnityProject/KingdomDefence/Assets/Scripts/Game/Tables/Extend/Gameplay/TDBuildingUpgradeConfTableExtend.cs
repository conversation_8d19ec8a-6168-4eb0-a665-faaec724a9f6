using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDBuildingUpgradeConfTable
    {
        private static Dictionary<int, Dictionary<int, TDBuildingUpgradeConf>> m_DictUpgradeConfs;

        static void CompleteRowAdd(TDBuildingUpgradeConf tdData)
        {
        }

        public static Dictionary<int, Dictionary<int, TDBuildingUpgradeConf>> GetDictUpgradeConfs()
        {
            if (m_DictUpgradeConfs == null)
            {
                m_DictUpgradeConfs = new Dictionary<int, Dictionary<int, TDBuildingUpgradeConf>>();
                foreach (var item in m_DataList)
                {
                    if (!m_DictUpgradeConfs.ContainsKey(item.upgradeId))
                    {
                        m_DictUpgradeConfs.Add(item.upgradeId, new Dictionary<int, TDBuildingUpgradeConf>());
                    }

                    m_DictUpgradeConfs[item.upgradeId].Add(item.level, item);
                }
            }

            return m_DictUpgradeConfs;
        }
        public static Dictionary<int, TDBuildingUpgradeConf> GetUpgradeConfs(int upgradeId)
        {
            if (GetDictUpgradeConfs().ContainsKey(upgradeId))
            {
                return m_DictUpgradeConfs[upgradeId];
            }

            return null;
        }

        public static TDBuildingUpgradeConf GetUpgradeConf(int upgradeId, int level)
        {
            if (GetDictUpgradeConfs().ContainsKey(upgradeId))
            {
                if (m_DictUpgradeConfs[upgradeId].ContainsKey(level))
                    return m_DictUpgradeConfs[upgradeId][level];
            }

            return null;
        }
    }
}