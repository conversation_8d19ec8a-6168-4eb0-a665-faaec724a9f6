using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDRunestoneConfTable
    {
        private static Dictionary<int, List<TDRunestoneConf>> m_DicItemsByQuality;
        private static Dictionary<int, int> m_DicItemsWeightByQuality;
        static void CompleteRowAdd(TDRunestoneConf tdData)
        {

        }
        public static Dictionary<int, List<TDRunestoneConf>> GetDicItemsByQuality()
        {
            if (m_DicItemsByQuality == null)
            {
                m_DicItemsByQuality = new Dictionary<int, List<TDRunestoneConf>>();
                foreach (var item in dataList)
                {
                    if (m_DicItemsByQuality.ContainsKey(item.rarity))
                    {
                        m_DicItemsByQuality[item.rarity].Add(item);
                    }
                    else
                    {
                        m_DicItemsByQuality.Add(item.rarity, new List<TDRunestoneConf>() { item });
                    }
                }
            }
            return m_DicItemsByQuality;
        }
        public static Dictionary<int, int> GetDicItemsWeightByQuality()
        {
            if (m_DicItemsWeightByQuality == null)
            {
                m_DicItemsWeightByQuality = new Dictionary<int, int>();
                foreach (var item in dataList)
                {
                    if (m_DicItemsWeightByQuality.ContainsKey(item.rarity))
                    {
                        m_DicItemsWeightByQuality[item.rarity] += item.weight;
                    }
                    else
                    {
                        m_DicItemsWeightByQuality.Add(item.rarity, 0);
                    }
                }
            }
            return m_DicItemsWeightByQuality;
        }
    }
}