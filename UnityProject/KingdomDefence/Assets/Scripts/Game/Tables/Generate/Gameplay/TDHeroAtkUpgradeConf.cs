//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDHeroAtkUpgradeConf
    {
      
        private EInt m_Id = 0;
        private EInt m_Level = 0;
        private EInt m_UpCost = 0;
        private EInt m_Atk = 0;
      
      //private Dictionary<string, TDUniversally.FieldData> m_DataCacheNoGenerate = new Dictionary<string, TDUniversally.FieldData>();
      
        /// <summary>
        /// ID
        /// </summary>
        public int id {get { return m_Id; } }
      
        /// <summary>
        /// 等级
        /// </summary>
        public int level {get { return m_Level; } }
      
        /// <summary>
        /// 升级价格
        /// </summary>
        public int upCost {get { return m_UpCost; } }
      
        /// <summary>
        /// 攻击力
        /// </summary>
        public int atk {get { return m_Atk; } }

        public void ReadRow(DataStreamReader dataR, int[] filedIndex)
        {
          //var schemeNames = dataR.GetSchemeName();
          int col = 0;
          while(true)
          {
            col = dataR.MoreFieldOnRow();
            if (col == -1)
            {
              break;
            }
            switch (filedIndex[col])
            { 
                case 0:
                    m_Id = dataR.ReadInt();
                    break;
                case 1:
                    m_Level = dataR.ReadInt();
                    break;
                case 2:
                    m_UpCost = dataR.ReadInt();
                    break;
                case 3:
                    m_Atk = dataR.ReadInt();
                    break;
                default:
                    //TableHelper.CacheNewField(dataR, schemeNames[col], m_DataCacheNoGenerate);
                    break;
            }
          }

          // 初始化枚举值，提前完成转换
          InitEnumValues();
        }
        
        // 初始化所有枚举值，提高性能
        private void InitEnumValues()
        {
        }
        
        /*
        public DataStreamReader.FieldType GetFieldTypeInNew(string fieldName)
        {
            if (m_DataCacheNoGenerate.ContainsKey(fieldName))
            {
                return m_DataCacheNoGenerate[fieldName].fieldType;
            }
            return DataStreamReader.FieldType.Unkown;
        }
        */
        
        public static Dictionary<string, int> GetFieldHeadIndex()
        {
          Dictionary<string, int> ret = new Dictionary<string, int>(4);
          
          ret.Add("Id", 0);
          ret.Add("Level", 1);
          ret.Add("UpCost", 2);
          ret.Add("Atk", 3);
          return ret;
        }
    }
}//namespace