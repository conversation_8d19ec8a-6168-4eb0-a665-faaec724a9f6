//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDLanguageEn
    {
      
        private string m_Id;
        private string m_Key;
      
      //private Dictionary<string, TDUniversally.FieldData> m_DataCacheNoGenerate = new Dictionary<string, TDUniversally.FieldData>();
      
        /// <summary>
        /// ID
        /// </summary>
        public string id {get { return m_Id; } }
      
        /// <summary>
        /// Key
        /// </summary>
        public string key {get { return m_Key; } }

        public void ReadRow(DataStreamReader dataR, int[] filedIndex)
        {
          //var schemeNames = dataR.GetSchemeName();
          int col = 0;
          while(true)
          {
            col = dataR.MoreFieldOnRow();
            if (col == -1)
            {
              break;
            }
            switch (filedIndex[col])
            { 
                case 0:
                    m_Id = dataR.ReadString();
                    break;
                case 1:
                    m_Key = dataR.ReadString();
                    break;
                default:
                    //TableHelper.CacheNewField(dataR, schemeNames[col], m_DataCacheNoGenerate);
                    break;
            }
          }

          // 初始化枚举值，提前完成转换
          InitEnumValues();
        }
        
        // 初始化所有枚举值，提高性能
        private void InitEnumValues()
        {
        }
        
        /*
        public DataStreamReader.FieldType GetFieldTypeInNew(string fieldName)
        {
            if (m_DataCacheNoGenerate.ContainsKey(fieldName))
            {
                return m_DataCacheNoGenerate[fieldName].fieldType;
            }
            return DataStreamReader.FieldType.Unkown;
        }
        */
        
        public static Dictionary<string, int> GetFieldHeadIndex()
        {
          Dictionary<string, int> ret = new Dictionary<string, int>(2);
          
          ret.Add("Id", 0);
          ret.Add("Key", 1);
          return ret;
        }
    }
}//namespace