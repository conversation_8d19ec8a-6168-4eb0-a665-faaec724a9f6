using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using TMPro;
using DG.Tweening;

namespace GameWish.Game
{
    public class ChapterExpBuff_Item : MonoBehaviour
    {
        [SerializeField] private Button m_BtnSelect;
        [SerializeField] private TextMeshProUGUI m_TxtDes;
        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private Image m_ImgBg;
        [SerializeField] private Image m_Ad;
        [SerializeField] private GameObject m_ObjEffQuality;
        [SerializeField] private TextMeshProUGUI m_TxtNew;
        private TDChapterBuffInfoConf m_BuffConf;
        private List<TDChapterBuffInfoConf> m_Results;
        private bool m_IsAd;
        private Vector2 m_RectInitPos;
        public int BuffId
        {
            get { return m_BuffConf.id; }
        }
        void OnEnable()
        {
            m_BtnSelect.onClick.AddListener(OnBtnSelect);
        }
        void OnDisable()
        {
            m_BtnSelect.onClick.RemoveAllListeners();
        }
        void OnBtnSelect()
        {
            if (m_IsAd)
            {
                AdsPlayMgr.S.PlayRewardAd("selectAdBuff", (click) =>
                {
                    transform.rectTransform().DOAnchorPosX(0, 0.2f);
                    ChapterBuffMgr.S.ChooseBuff(m_BuffConf, m_Results);
                    EventSystem.S.Send(EventID.OnSelectChapterBuff, m_BuffConf.id);
                });
            }
            else
            {
                transform.rectTransform().DOAnchorPosX(0, 0.2f);
                ChapterBuffMgr.S.ChooseBuff(m_BuffConf, m_Results);
                EventSystem.S.Send(EventID.OnSelectChapterBuff, m_BuffConf.id);
            }
            m_BtnSelect.interactable = false;
        }
        public void Init()
        {
            m_RectInitPos = transform.rectTransform().anchoredPosition;
        }
        public void SetInfo(AbstractPanel panel, List<TDChapterBuffInfoConf> results, int index, bool isAd)
        {
            transform.rectTransform().anchoredPosition = m_RectInitPos;
            m_BtnSelect.interactable = true;
            m_IsAd = isAd;
            m_Ad.enabled = m_IsAd;
            m_Results = results;
            m_BuffConf = results[index];
            m_TxtDes.text = TDLanguageTable.Get(m_BuffConf.buffDes);
            m_ObjEffQuality.SetObjActive(m_BuffConf.buffQuality == 3);
            panel.FindSpriteAsync(m_BuffConf.icon, (s) =>
            {
                m_ImgIcon.sprite = s;
                m_ImgIcon.SetNativeSize();
            }, true);
            panel.FindSpriteAsync(UISpriteUtils.GetChapterBuffBgByQuality(m_BuffConf.buffQuality), (s) =>
            {
                m_ImgBg.sprite = s;
            }, true);
            // m_TxtNew.enabled = !ChapterBuffMgr.S.HasBuffId(m_BuffConf.id);
            m_TxtNew.enabled = false;
        }
        public void HideAnim()
        {
            // foreach (var item in GetComponentsInChildren<ElementUIAnimCustomStart>())
            // {
            //     item.HideAnim();
            // }
            transform.localScale = Vector3.zero;
        }
    }
}
