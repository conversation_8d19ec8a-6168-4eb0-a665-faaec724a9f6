using System.Net.Mime;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using PrimeTween;

namespace GameWish.Game
{
    public class HitNumTips : WorldUIBindTransform
    {
        [SerializeField] private Text m_TxtNum;
        [SerializeField] private Text m_TxtNumCrit;
        [SerializeField] private AnimationCurve m_AnimCurve;
        private int m_TotalDmg;
        private bool m_Hide = false;

        /// <summary>
        /// 直接跳伤害 不累加
        /// </summary>
        public void ShowNum(int deltaDmg, bool showColor = false, bool isCrit = false)
        {
            m_Hide = false;
            if (isCrit)
            {
                m_TxtNum.gameObject.SetActive(false);
                m_TxtNumCrit.gameObject.SetActive(true);
            }
            else
            {
                m_TxtNum.gameObject.SetActive(true);
                m_TxtNumCrit.gameObject.SetActive(false);
                if (showColor)
                {
                    if (deltaDmg < 0)
                    {
                        m_TxtNum.color = Color.red;
                    }
                    else
                    {
                        m_TxtNum.color = Color.green;
                    }
                }
                else
                {
                    m_TxtNum.color = Color.white;
                }
            }

            m_TxtNumCrit.text = m_TxtNum.text = deltaDmg.ToString();
            transform.localScale = Vector3.zero;

            Sequence.Create().Chain(Tween.Scale(transform, Vector3.one * 0.5f, 0.2f, Ease.OutBack)).ChainDelay(0.2f)
                .Chain(Tween.Scale(transform, Vector3.zero, 0.15f)).OnComplete(() =>
                {
                    WorldNormalUIPanel.S.RecylePoolObj(gameObject);
                });
        }

        public void Hide(bool followTrans = false)
        {
            if (!followTrans)
            {
                followTransform = null;
            }
        }
    }
}