using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;

namespace Game.Tool
{
    public class SceneResourcesTool
    {
        // 获取多个目录下的所有文件数据
        public static List<Object> GetFiles(string filter, Object[] folders)
        {
            //获取所有目录的路径
            List<string> paths = new List<string>();
            foreach (var folder in folders)
            {
                paths.Add(GetFolderPath(folder));
            }

            //获取路径下的所有文件（包含所有子文件夹）
            List<Object> fileList = GetAsset(filter, paths.ToArray());
            return fileList;
        }

        //------------------------------------------------------------------------------------------------------GetFolderPath
        /// 获取指定目录的路径
        static public string GetFolderPath(Object folder)
        {
            string path = null;
            if (folder != null)
            {
                path = AssetDatabase.GetAssetPath(folder);
                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                {
                    //如果是目录获得目录名，如果是文件获得文件所在的目录名
                    path = Path.GetDirectoryName(path);
                }
            }

            return path;
        }

        //------------------------------------------------------------------------------------------------------GetAsset
        /// 获取多个路径下的资源（卧槽原来这个已经包含了子目录的所有资源）
        static public List<Object> GetAsset(string filter, string[] searchInPaths)
        {
            List<Object> fileList = new List<Object>();
            if (searchInPaths != null)
            {
                //排除空的路径
                List<string> finalPaths = new List<string>();
                foreach (var path in searchInPaths)
                {
                    if (!string.IsNullOrEmpty(path)) finalPaths.Add(path);
                }

                //避免空路径导致遍历了整个游戏目录
                if (finalPaths.Count > 0)
                {
                    //获取所有路径下的资源GUID（卧槽这傻逼API如果路径为空会遍历整个游戏目录！）
                    // string[] allGUID = AssetDatabase.FindAssets("t:Prefab", finalPaths.ToArray());
                    string[] allGUID = AssetDatabase.FindAssets(filter, finalPaths.ToArray());

                    //循环遍历每个资源路径
                    for (int i = 0; i < allGUID.Length; i++)
                    {
                        //根据GUID获取对应的资源路径
                        string path = AssetDatabase.GUIDToAssetPath(allGUID[i]);
                        //根据路径加载资源
                        var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject));
                        if (obj != null) fileList.Add(obj);
                    }
                }
            }

            return fileList;
        }
    }
}