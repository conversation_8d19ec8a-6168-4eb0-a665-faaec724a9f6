{"ErrorExceptions": [{"ValidationTest": "API Validation", "ExceptionMessage": "Adding the first entry in includePlatforms requires a new major version. Was:\"\" Now:\"Android, Editor, GameCoreScarlett, GameCoreXboxOne, LinuxStandalone64, CloudRendering, Lumin, macOSStandalone, PS4, PS5, Stadia, Switch, WSA, WebGL, WindowsStandalone32, WindowsStandalone64, XboxOne\"", "PackageVersion": "4.11.0"}, {"ValidationTest": "API Validation", "ExceptionMessage": "Adding the first entry in includePlatforms requires a new major version. Was:\"\" Now:\"Editor, GameCore<PERSON>, GameCoreXboxOne, LinuxStandalone64, CloudRendering, Lumin, PS4, PS5, Stadia, Switch, WSA, WebGL, WindowsStandalone32, WindowsStandalone64, XboxOne\"", "PackageVersion": "4.11.0"}], "WarningExceptions": []}